'use client';

import { SchemaBuilderStepEnum, useSchemaBuilderContext } from '@/contexts/SchemaBuilderContext';
import { SchemaOfferingNavbar } from './components/SchemaBuilderNavbar';
import { DeploymentType } from '@/types/deployments';
import { useDeploymentDetails } from '@/hooks/useDeploymentDetails';
import { useGetAndSetSchemasOfIssuer } from '@/hooks/useGetAndSetSchemasOfIssuer';
import { useSchemaOfferingForm } from '@/hooks/useSchemaOfferingForm';
import { useTranslations } from 'next-intl';
import { ButtonGradient } from '@/components/Buttons';
import { useState } from 'react';

const TYPE = DeploymentType.ISSUER;

export const SchemaOffering = () => {
    const t = useTranslations('schema_offering');
    const { activeVersionId, setStep } = useSchemaBuilderContext();
    const { data } = useDeploymentDetails({ type: TYPE });
    const { data: schemas = [] } = useGetAndSetSchemasOfIssuer({
        fullHost: data?.fullHost,
        authorization: data?.authKey || undefined,
    });

    const [showJsonPreview, setShowJsonPreview] = useState(false);

    const schema = schemas.find(s => s.id === activeVersionId);
    if (!schema) throw new Error('No schema found');

    const { fieldsToRender, onSubmit, isValid, isSubmitting, credentialSubjectSchema } = useSchemaOfferingForm({
        schema,
    });

    return (
        <div className="px-10 flex flex-col items-start gap-4 h-[calc(100vh_-_200px)]">
            <SchemaOfferingNavbar onBackButtonClick={() => setStep(SchemaBuilderStepEnum.SCHEMA_LIST)} />
            <div className="flex flex-row justify-between gap-4 overflow-auto without-scrollbar">
                <div className="flex flex-row gap-6 h-full flex-1  p-4 rounded-lg bg-main-600/5 ">
                    {/* Form Column */}
                    <div className="flex-1 flex flex-col gap-4">
                        <div className="flex flex-row justify-between items-center">
                            <div>
                                <h1 className="text-xl font-semibold">{t('title')}</h1>
                                <p className="text-sm text-gray-600">
                                    {schema.name} - {t('form_description')}
                                </p>
                            </div>
                            {/* <button
                            id="components_SchemaManager_SchemaOffering_button_kxle3r"
                            onClick={() => setShowJsonPreview(!showJsonPreview)}
                            className="text-sm text-blue-600 hover:text-blue-800 underline"
                        >
                            {showJsonPreview ? t('hide_schema') : t('show_schema')}
                        </button> */}
                        </div>

                        {/* Form */}
                        <form
                            onSubmit={onSubmit}
                            className="flex flex-col gap-4 flex-1 overflow-y-auto without-scrollbar"
                        >
                            <div className="grid grid-cols-1 gap-4">{fieldsToRender}</div>

                            <div className="flex justify-end pt-4 border-t">
                                <ButtonGradient
                                    id="components_SchemaManager_SchemaOffering_button_5wnohp"
                                    type="submit"
                                    disabled={!isValid}
                                    isLoading={isSubmitting}
                                >
                                    {t('submit_button')}
                                </ButtonGradient>
                            </div>
                        </form>
                    </div>
                </div>
                {/* JSON Preview Column */}
                {!showJsonPreview && (
                    <div className="w-1/2 flex flex-col gap-4 bg-main-600/5 rounded-lg p-4">
                        <h2 className="text-lg font-semibold">{t('schema_preview')}</h2>
                        <div className="flex-1 rounded-lg p-4 overflow-auto">
                            <pre className="text-xs">{JSON.stringify(credentialSubjectSchema, null, 2)}</pre>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
