# Schema Offering Form - Dokumentacja

## 📋 Przegląd

<PERSON>mentowano automatyczne generowanie formularza na podstawie JSON Schema dla komponentu `SchemaOffering`. System automatycznie tworzy formularz z walidacją na podstawie struktury `credentialSubject` ze schematu.

## 🏗️ Architektura

### 1. **Walida<PERSON>ja Schema** (`validation/schemaOfferingValidation.ts`)
- **`createSchemaOfferingValidation`** - Dynamicznie tworzy schema walidacji Zod na podstawie JSON Schema
- **`createSchemaOfferingFormTypes`** - Mapuje typy pól JSON Schema na FormFieldsTypes
- **`createSelectOptionsFromSchema`** - Tworzy opcje select dla pól z enum

**Obsługiwane typy pól:**
- `string` → `FormFieldsTypes.INPUT`
- `string` z `format: "date-time"` → `FormFieldsTypes.DATETIME`
- `string` z `format: "email"` → `FormFieldsTypes.INPUT` (z walidacją email)
- `string` z `format: "uri"` → `FormFieldsTypes.INPUT` (z walidacją URL)
- `string` z `enum` → `FormFieldsTypes.SELECT`
- `number` → `FormFieldsTypes.INPUT`
- `boolean` → `FormFieldsTypes.CHECKBOX`

### 2. **Hook Formularza** (`hooks/useSchemaOfferingForm.tsx`)
- Używa `react-hook-form` z `zodResolver`
- Automatycznie generuje pola formularza na podstawie schema
- Obsługuje walidację w czasie rzeczywistym
- Integruje się z istniejącym systemem `renderFormFields`

**Kluczowe funkcje:**
- `onSubmit` - Handler wysyłania formularza (obecnie console.log)
- `getFieldLabel` - Pobiera label z schema.title
- `getFieldDescription` - Pobiera opis z schema.description
- `isFieldRequired` - Sprawdza czy pole jest wymagane

### 3. **Komponent UI** (`components/SchemaManager/SchemaOffering.tsx`)
- Dwukolumnowy layout: formularz + podgląd schema (opcjonalny)
- Responsywny design zgodny z resztą aplikacji
- Przycisk toggle do pokazywania/ukrywania podglądu JSON
- Integracja z systemem tłumaczeń

## 🎯 Funkcjonalności

### ✅ **Zaimplementowane:**
1. **Automatyczne generowanie formularza** na podstawie JSON Schema
2. **Walidacja w czasie rzeczywistym** z Zod + react-hook-form
3. **Obsługa różnych typów pól** (text, email, datetime, select, checkbox)
4. **Wymagane/opcjonalne pola** na podstawie schema.required
5. **Tłumaczenia** (system next-intl)
6. **Responsywny design** zgodny z aplikacją
7. **Podgląd JSON Schema** (toggle)
8. **Automatyczne ID dla przycisków** (tracking)

### 🔄 **Do implementacji w przyszłości:**
1. **API Integration** - Rzeczywiste wysyłanie danych
2. **Deep Links** - Integracja z aplikacją mobilną
3. **Nested Objects** - Obsługa zagnieżdżonych obiektów
4. **Array Fields** - Obsługa pól tablicowych
5. **Custom Validators** - Dodatkowe walidatory specyficzne dla domeny

## 🚀 Użycie

### Podstawowe użycie:
```tsx
// Komponent automatycznie pobiera schema z kontekstu
<SchemaOffering />
```

### Struktura Schema:
```json
{
  "properties": {
    "credentialSubject": {
      "type": "object",
      "properties": {
        "firstName": {
          "type": "string",
          "title": "First Name",
          "description": "Enter your first name"
        },
        "email": {
          "type": "string",
          "format": "email",
          "title": "Email Address"
        },
        "birthDate": {
          "type": "string",
          "format": "date-time",
          "title": "Birth Date"
        },
        "gender": {
          "type": "string",
          "enum": ["Male", "Female", "Other"],
          "title": "Gender"
        }
      },
      "required": ["firstName", "email"]
    }
  }
}
```

## 🔧 Konfiguracja

### Tłumaczenia (`locales/en.json`):
```json
{
  "schema_offering": {
    "title": "Schema Offering Form",
    "form_description": "Fill out the form to create a credential offering",
    "submit_button": "Submit Credential",
    "show_schema": "Show Schema",
    "hide_schema": "Hide Schema",
    "schema_preview": "Schema Preview"
  },
  "validation_messages": {
    "required_field": "This field is required",
    "datetime_required": "Date and time is required",
    "uri_invalid_format": "Please enter a valid URL",
    "email_invalid_format": "Please enter a valid email address",
    "checkbox_must_be_checked": "This checkbox must be checked"
  }
}
```

## 🧪 Testowanie

### Przykład testowania formularza:
1. Przejdź do Schema Builder
2. Wybierz opublikowany schema
3. Kliknij "Schema Offering"
4. Wypełnij formularz zgodnie z polami ze schema
5. Sprawdź walidację (wymagane pola, formaty)
6. Kliknij "Submit Credential"
7. Sprawdź console.log z danymi formularza

### Testowanie różnych typów pól:
- **Text Input**: Podstawowe pola tekstowe
- **Email**: Walidacja formatu email
- **DateTime**: Picker daty i czasu
- **Select**: Dropdown z opcjami z enum
- **Checkbox**: Pola boolean

## 📁 Struktura Plików

```
validation/
├── schemaOfferingValidation.ts    # Walidacja Zod + mapowanie typów

hooks/
├── useSchemaOfferingForm.tsx      # Hook react-hook-form

components/SchemaManager/
├── SchemaOffering.tsx             # Główny komponent UI

locales/
├── en.json                        # Tłumaczenia (rozszerzone)
```

## 🎨 Styling

Komponent używa Tailwind CSS i jest zgodny z designem aplikacji:
- **Layout**: Flexbox dwukolumnowy
- **Kolory**: Zgodne z paletą main-*
- **Spacing**: gap-4, p-4 (konsystentne)
- **Typography**: text-xl, text-sm (hierarchia)
- **Buttons**: ButtonGradient (główny CTA)

## 🔍 Debug

### Console Logs:
Formularz loguje szczegółowe informacje przy submit:
```javascript
{
  schemaId: "schema-uuid",
  schemaName: "BirthCertificate", 
  schemaType: "credential-type",
  formData: { /* dane formularza */ },
  credentialSubject: { /* dane do API */ }
}
```

### Błędy walidacji:
- Sprawdź czy schema ma `properties.credentialSubject`
- Sprawdź czy pola mają poprawne typy
- Sprawdź tłumaczenia dla komunikatów błędów
