// Test funkcji formatowania camelCase na czytelny tekst
const formatCamelCaseToReadable = (text) => {
    return text
        // Dodaj spację przed wielką literą (ale nie na początku)
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        // Pierwsza litera wielka
        .replace(/^./, str => str.toUpperCase());
};

// Test funkcji bezpiecznego tłumaczenia
const getSafeTranslation = (t, localeKey, fallbackKey) => {
    try {
        const translation = t(localeKey);
        // Sprawdź czy tłumaczenie istnieje i nie jest puste
        if (translation && translation !== localeKey) {
            return translation;
        }
        // Jeśli tłumaczenie nie istnieje lub jest równe kluczowi, użyj sformatowanego fallbacku
        return formatCamelCaseToReadable(fallbackKey);
    } catch (error) {
        // W przypadku błędu, uż<PERSON>j sformatowanego fallbacku
        return formatCamelCaseToReadable(fallbackKey);
    }
};

// Przykłady testowe
console.log('=== Testy formatowania camelCase ===');
console.log('opisNazwaTest ->', formatCamelCaseToReadable('opisNazwaTest'));
console.log('firstName ->', formatCamelCaseToReadable('firstName'));
console.log('emailAddress ->', formatCamelCaseToReadable('emailAddress'));
console.log('phoneNumber ->', formatCamelCaseToReadable('phoneNumber'));
console.log('dateOfBirth ->', formatCamelCaseToReadable('dateOfBirth'));

console.log('\n=== Testy bezpiecznego tłumaczenia ===');

// Mock funkcji t() która zwraca tłumaczenie
const mockTWithTranslations = (key) => {
    const translations = {
        'first_name_label': 'Imię',
        'email_address_label': 'Adres email'
    };
    return translations[key] || key;
};

// Mock funkcji t() która rzuca błąd
const mockTWithError = (key) => {
    throw new Error('Translation not found');
};

// Mock funkcji t() która zwraca pusty string
const mockTWithEmpty = (key) => '';

console.log('Z prawidłowym tłumaczeniem:');
console.log('firstName ->', getSafeTranslation(mockTWithTranslations, 'first_name_label', 'firstName'));
console.log('emailAddress ->', getSafeTranslation(mockTWithTranslations, 'email_address_label', 'emailAddress'));

console.log('\nBez tłumaczenia (fallback):');
console.log('opisNazwaTest ->', getSafeTranslation(mockTWithTranslations, 'opis_nazwa_test_label', 'opisNazwaTest'));

console.log('\nZ błędem tłumaczenia:');
console.log('firstName ->', getSafeTranslation(mockTWithError, 'first_name_label', 'firstName'));

console.log('\nZ pustym tłumaczeniem:');
console.log('firstName ->', getSafeTranslation(mockTWithEmpty, 'first_name_label', 'firstName'));
