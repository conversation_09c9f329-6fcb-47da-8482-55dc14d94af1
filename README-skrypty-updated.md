# Skrypt do automatycznego dodawania ID do komponentów i elementów

## Opis

Skrypt bash automatycznie dodaje unikalne ID do wszystkich komponentów i elementów w folderach `/app` i `/components`:

- **Komponenty React**: `<Button*`, `<Link*`
- **Elementy HTML**: `<button`, `<a`

## Wzorce ID

### **Dla plików z folderu `/app`:**
`nazwa_podstrony_nazwaKomponentu_button/link_randomowyHash`

### **Dla plików z folderu `/components`:**
`components_nazwaKomponentu_button/link_randomowyHash`

## Obsługiwane elementy

### ✅ **Komponenty React:**
- `<ButtonGradient` → `button_hash`
- `<ButtonBorder` → `button_hash`
- `<ButtonBorderSmall` → `button_hash`
- `<Link` → `link_hash`
- `<LinkGradient` → `link_hash`
- `<LinkBorder` → `link_hash`

### ✅ **Elementy HTML:**
- `<button` → `button_hash`
- `<a` → `link_hash`

## Prz<PERSON>łady transformacji

**Przed:**
```jsx
<ButtonGradient type="submit">Submit</ButtonGradient>
<button onClick={handleClick}>Click me</button>
<Link href="/dashboard">Dashboard</Link>
<a href="/home">Home</a>
```

**Po:**
```jsx
<ButtonGradient id="auth_LoginScreen_button_abc123" type="submit">Submit</ButtonGradient>
<button id="auth_LoginScreen_button_def456" onClick={handleClick}>Click me</button>
<Link id="auth_LoginScreen_link_ghi789" href="/dashboard">Dashboard</Link>
<a id="auth_LoginScreen_link_jkl012" href="/home">Home</a>
```

## Bezpieczeństwo

✅ **Skrypt jest bezpieczny:**
- Nie modyfikuje elementów, które już mają atrybut `id`
- Wyświetla szczegółowe logi o zmianach
- Nie usuwa ani nie nadpisuje istniejących atrybutów
- Przetwarza tylko pliki `.tsx` i `.jsx`

## Uruchomienie

```bash
chmod +x add-ids-to-components.sh
./add-ids-to-components.sh
```

## Przykładowy output

```
🚀 Rozpoczynam dodawanie ID do komponentów Button, Link, button i a...
📁 Przeszukuję folder: ./app
📁 Przeszukuję folder: ./components
📄 Znaleziono 45 plików .tsx/.jsx

Przetwarzam plik: app/(auth)/page.tsx
  Dodano ID do ButtonGradient: auth_LoginScreen_button_abc123
  Dodano ID do button: auth_LoginScreen_button_def456
  Dodano ID do Link: auth_LoginScreen_link_ghi789
  Dodano ID do a: auth_LoginScreen_link_jkl012
  ✅ Plik został zmodyfikowany

Przetwarzam plik: components/Modals/ModalLogic.show_logs.tsx
  Dodano ID do ButtonBorderBasic: components_ModalLogic.show_logs_button_mno345
  ✅ Plik został zmodyfikowany

✨ Zakończono!
📊 Zmodyfikowano 15 z 45 plików
```

## Logika priorytetów

Skrypt sprawdza elementy w następującej kolejności:
1. **Button komponenty** (`<Button*`) - najwyższy priorytet
2. **HTML button** (`<button`) - tylko jeśli nie ma Button komponentu
3. **Link komponenty** (`<Link*`) - tylko jeśli nie ma Button/button
4. **HTML a** (`<a`) - najniższy priorytet, tylko jeśli nie ma innych

To zapewnia, że każda linia zostanie przetworzona tylko raz i otrzyma odpowiedni typ ID.
